#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的时间戳处理逻辑
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_time_convert():
    """测试时间转换函数"""
    from funasr_api_zm import time_convert
    
    print("测试时间转换函数:")
    
    # 测试用例
    test_cases = [
        (0, "00:00:00,000"),
        (1000, "00:00:01,000"),
        (61000, "00:01:01,000"),
        (3661500, "01:01:01,500"),
        (125000, "00:02:05,000")
    ]
    
    for ms, expected in test_cases:
        result = time_convert(ms)
        status = "✓" if result == expected else "✗"
        print(f"  {status} {ms}ms -> {result} (期望: {expected})")

def test_text2srt():
    """测试Text2SRT类"""
    from funasr_api_zm import Text2SRT
    
    print("\n测试Text2SRT类:")
    
    # 模拟句子时间戳数据（毫秒格式）
    test_timestamp = [[0, 2000], [2000, 4000]]  # 0-2秒，2-4秒
    test_text = "这是一个测试句子"
    
    t2s = Text2SRT(test_text, test_timestamp)
    
    print(f"  文本: {t2s.text()}")
    print(f"  开始时间: {t2s.start_time}")
    print(f"  结束时间: {t2s.end_time}")
    print(f"  SRT格式:")
    print(f"    {t2s.srt()}")

def test_sentence_processing():
    """测试句子级处理逻辑"""
    print("\n测试句子级处理逻辑:")
    
    # 模拟FunASR返回的句子级数据
    mock_sentence_info = [
        {
            'text': '这是第一个句子。',
            'timestamp': [[0, 2000]]  # 0-2秒，已转换为毫秒
        },
        {
            'text': '这是第二个句子。',
            'timestamp': [[2000, 4500]]  # 2-4.5秒
        },
        {
            'text': '这是第三个句子。',
            'timestamp': [[4500, 7000]]  # 4.5-7秒
        }
    ]
    
    # 模拟生成SRT
    from funasr_api_zm import FunASRTranscriber
    
    # 创建一个模拟的转录器实例（不需要真实的模型）
    class MockTranscriber:
        def generate_srt_from_sentences(self, sentence_list):
            from funasr_api_zm import Text2SRT
            srt_total = ''
            for i, sent in enumerate(sentence_list):
                t2s = Text2SRT(sent['text'], sent['timestamp'])
                if 'spk' in sent:
                    srt_total += "{}  spk{}\n{}".format(i + 1, sent['spk'], t2s.srt())
                else:
                    srt_total += "{}\n{}\n".format(i + 1, t2s.srt())
            return srt_total
    
    transcriber = MockTranscriber()
    srt_content = transcriber.generate_srt_from_sentences(mock_sentence_info)
    
    print("  生成的SRT内容:")
    print(srt_content)

def test_scaling_factor():
    """测试缩放因子16的效果"""
    print("\n测试缩放因子16的效果:")
    
    # 模拟原始时间戳（秒）
    original_timestamps = [
        [0.0, 2.0],    # 0-2秒
        [2.0, 4.5],    # 2-4.5秒
        [4.5, 7.0]     # 4.5-7秒
    ]
    
    print("  原始时间戳（秒）-> 缩放后时间戳（毫秒）:")
    for i, (start, end) in enumerate(original_timestamps):
        scaled_start = int(start * 16)
        scaled_end = int(end * 16)
        print(f"    句子{i+1}: [{start:.1f}s, {end:.1f}s] -> [{scaled_start}ms, {scaled_end}ms]")

if __name__ == "__main__":
    print("=" * 50)
    print("测试修改后的时间戳处理逻辑")
    print("=" * 50)
    
    try:
        test_time_convert()
        test_text2srt()
        test_sentence_processing()
        test_scaling_factor()
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
