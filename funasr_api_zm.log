nohup: ignoring input
2025-08-20 21:02:46,792 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-20 21:02:47,017 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-20 21:02:47,053 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-20 21:02:47,055 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-20 21:03:02,097 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-20 21:03:02,142 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-20 21:03:02,156 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-20 21:03:02,304 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
INFO:     Started server process [1797608]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvic<PERSON> running on http://0.0.0.0:10093 (Press CTRL+C to quit)
funasr version: 1.2.6.
Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
funasr version: 1.2.6.
Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
funasr version: 1.2.6.
Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
funasr version: 1.2.6.
Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
New version is available: 1.2.7.
Please use the command "pip install -U funasr" to upgrade.
New version is available: 1.2.7.
Please use the command "pip install -U funasr" to upgrade.
New version is available: 1.2.7.
Please use the command "pip install -U funasr" to upgrade.
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/SenseVoiceSmall
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/SenseVoiceSmall
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/SenseVoiceSmall
New version is available: 1.2.7.
Please use the command "pip install -U funasr" to upgrade.
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/SenseVoiceSmall
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
初始化翻译服务，连接到: http://**************:5006
初始化翻译服务，连接到: http://**************:5006
初始化翻译服务，连接到: http://**************:5006
INFO:     Started server process [1797608]
INFO:     Waiting for application startup.
INFO:     Started server process [1797608]
INFO:     Application startup complete.
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:10094 (Press CTRL+C to quit)
INFO:     Uvicorn running on http://0.0.0.0:10097 (Press CTRL+C to quit)
初始化翻译服务，连接到: http://**************:5006
INFO:     Started server process [1797608]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:10098 (Press CTRL+C to quit)
Traceback (most recent call last):
  File "/data/funasr/funasr_api_zm.py", line 671, in asr_turbo
    raise ValueError(f"无法下载音频文件，状态码: {response.status_code}")
ValueError: 无法下载音频文件，状态码: 404
Received JSON: {'wavurl': 'http://ftp.bianfusou11.cn/externalLinksController/downloadFileByKey/3620f8cace15470496dc0bd4b74043a0.mp4?dkey=d6e40b49-a2f5-4d1d-bac7-e82be06bd88c'}
开始下载音频文件: http://ftp.bianfusou11.cn/externalLinksController/downloadFileByKey/3620f8cace15470496dc0bd4b74043a0.mp4?dkey=d6e40b49-a2f5-4d1d-bac7-e82be06bd88c
API处理出错: 无法下载音频文件，状态码: 404
INFO:     **************:57502 - "POST /api/sound/asr_turbo HTTP/1.1" 200 OK
Received JSON: {'wavurl': 'https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/20250402/dff3f8157d9dce1ed5fbce4d9167ce50_082600_104138_1611a.mp4?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2690952715&Signature=QMqOD%2FWypnuUWLo%2BlOF9mD3l28g%3D'}
开始下载音频文件: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/20250402/dff3f8157d9dce1ed5fbce4d9167ce50_082600_104138_1611a.mp4?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2690952715&Signature=QMqOD%2FWypnuUWLo%2BlOF9mD3l28g%3D
将音频内容写入临时文件: /data/lyraChatGLM/temp_ask_wav_path/20250820_210825_7.26428.wav
音频文件已保存，大小: 2717341993 字节
开始音频转录...
开始转录音频文件: /data/lyraChatGLM/temp_ask_wav_path/20250820_210825_7.26428.wav

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
100%|[34m██████████[0m| 1/1 [00:42<00:00, 42.46s/it]
{'load_data': '23.205', 'extract_feat': '19.143', 'forward': '42.456', 'batch_size': '1', 'rtf': '1.112'}, : 100%|[34m██████████[0m| 1/1 [00:42<00:00, 42.46s/it]
rtf_avg: 1.112: 100%|[34m██████████[0m| 1/1 [00:42<00:00, 42.46s/it]                                                                                             
rtf_avg: 1.112: 100%|[34m██████████[0m| 1/1 [00:42<00:00, 42.46s/it]

  0%|[31m          [0m| 0/1 [00:00<?, ?it/s]

  0%|[34m          [0m| 0/39 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 39/39 [00:00<00:00, 184.19it/s][A

{'load_data': '0.000', 'extract_feat': '0.049', 'forward': '0.212', 'batch_size': '39', 'rtf': '0.009'}, : 100%|[34m██████████[0m| 39/39 [00:00<00:00, 184.19it/s][A

rtf_avg: 0.009: 100%|[34m██████████[0m| 39/39 [00:00<00:00, 184.19it/s]                                                                                           [A
rtf_avg: 0.009: 100%|[34m██████████[0m| 39/39 [00:00<00:00, 178.68it/s]


  0%|[34m          [0m| 0/16 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 16/16 [00:00<00:00, 138.79it/s][A

{'load_data': '0.000', 'extract_feat': '0.038', 'forward': '0.115', 'batch_size': '16', 'rtf': '0.003'}, : 100%|[34m██████████[0m| 16/16 [00:00<00:00, 138.79it/s][A

rtf_avg: 0.003: 100%|[34m██████████[0m| 16/16 [00:00<00:00, 138.79it/s]                                                                                           [A
rtf_avg: 0.003: 100%|[34m██████████[0m| 16/16 [00:00<00:00, 134.63it/s]


  0%|[34m          [0m| 0/12 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 12/12 [00:00<00:00, 106.26it/s][A

{'load_data': '0.000', 'extract_feat': '0.038', 'forward': '0.113', 'batch_size': '12', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 12/12 [00:00<00:00, 106.26it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 12/12 [00:00<00:00, 106.26it/s]                                                                                           [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 12/12 [00:00<00:00, 102.42it/s]


  0%|[34m          [0m| 0/10 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 10/10 [00:00<00:00, 89.10it/s][A

{'load_data': '0.000', 'extract_feat': '0.038', 'forward': '0.112', 'batch_size': '10', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 10/10 [00:00<00:00, 89.10it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 10/10 [00:00<00:00, 89.10it/s]                                                                                           [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 10/10 [00:00<00:00, 86.41it/s]


  0%|[34m          [0m| 0/9 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 9/9 [00:00<00:00, 81.35it/s][A

{'load_data': '0.000', 'extract_feat': '0.037', 'forward': '0.111', 'batch_size': '9', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 9/9 [00:00<00:00, 81.35it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 9/9 [00:00<00:00, 81.35it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 9/9 [00:00<00:00, 78.86it/s]


  0%|[34m          [0m| 0/8 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 8/8 [00:00<00:00, 72.90it/s][A

{'load_data': '0.000', 'extract_feat': '0.035', 'forward': '0.110', 'batch_size': '8', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 8/8 [00:00<00:00, 72.90it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 8/8 [00:00<00:00, 72.90it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 8/8 [00:00<00:00, 70.63it/s]


  0%|[34m          [0m| 0/8 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 8/8 [00:00<00:00, 67.05it/s][A

{'load_data': '0.000', 'extract_feat': '0.038', 'forward': '0.119', 'batch_size': '8', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 8/8 [00:00<00:00, 67.05it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 8/8 [00:00<00:00, 67.05it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 8/8 [00:00<00:00, 64.71it/s]


  0%|[34m          [0m| 0/7 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 7/7 [00:00<00:00, 64.43it/s][A

{'load_data': '0.000', 'extract_feat': '0.034', 'forward': '0.109', 'batch_size': '7', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 7/7 [00:00<00:00, 64.43it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 7/7 [00:00<00:00, 64.43it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 7/7 [00:00<00:00, 61.97it/s]


  0%|[34m          [0m| 0/7 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 7/7 [00:00<00:00, 62.82it/s][A

{'load_data': '0.000', 'extract_feat': '0.036', 'forward': '0.111', 'batch_size': '7', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 7/7 [00:00<00:00, 62.82it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 7/7 [00:00<00:00, 62.82it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 7/7 [00:00<00:00, 60.48it/s]


  0%|[34m          [0m| 0/7 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 7/7 [00:00<00:00, 58.56it/s][A

{'load_data': '0.000', 'extract_feat': '0.037', 'forward': '0.120', 'batch_size': '7', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 7/7 [00:00<00:00, 58.56it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 7/7 [00:00<00:00, 58.56it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 7/7 [00:00<00:00, 56.52it/s]


  0%|[34m          [0m| 0/6 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 6/6 [00:00<00:00, 55.87it/s][A

{'load_data': '0.000', 'extract_feat': '0.033', 'forward': '0.107', 'batch_size': '6', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 6/6 [00:00<00:00, 55.87it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 55.87it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 53.74it/s]


  0%|[34m          [0m| 0/6 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 6/6 [00:00<00:00, 55.25it/s][A

{'load_data': '0.000', 'extract_feat': '0.033', 'forward': '0.109', 'batch_size': '6', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 6/6 [00:00<00:00, 55.25it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 55.25it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 53.15it/s]


  0%|[34m          [0m| 0/6 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 6/6 [00:00<00:00, 54.78it/s][A

{'load_data': '0.000', 'extract_feat': '0.034', 'forward': '0.110', 'batch_size': '6', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 6/6 [00:00<00:00, 54.78it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 54.78it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 52.71it/s]


  0%|[34m          [0m| 0/6 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 6/6 [00:00<00:00, 53.15it/s][A

{'load_data': '0.000', 'extract_feat': '0.034', 'forward': '0.113', 'batch_size': '6', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 6/6 [00:00<00:00, 53.15it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 53.15it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 51.22it/s]


  0%|[34m          [0m| 0/6 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 6/6 [00:00<00:00, 49.77it/s][A

{'load_data': '0.000', 'extract_feat': '0.039', 'forward': '0.121', 'batch_size': '6', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 6/6 [00:00<00:00, 49.77it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 49.77it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 48.05it/s]


  0%|[34m          [0m| 0/6 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 6/6 [00:00<00:00, 50.31it/s][A

{'load_data': '0.000', 'extract_feat': '0.036', 'forward': '0.119', 'batch_size': '6', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 6/6 [00:00<00:00, 50.31it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 50.31it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 48.55it/s]


  0%|[34m          [0m| 0/6 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 6/6 [00:00<00:00, 49.71it/s][A

{'load_data': '0.000', 'extract_feat': '0.037', 'forward': '0.121', 'batch_size': '6', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 6/6 [00:00<00:00, 49.71it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 49.71it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 47.99it/s]


  0%|[34m          [0m| 0/6 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 6/6 [00:00<00:00, 48.82it/s][A

{'load_data': '0.000', 'extract_feat': '0.038', 'forward': '0.123', 'batch_size': '6', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 6/6 [00:00<00:00, 48.82it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 48.82it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 6/6 [00:00<00:00, 47.15it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 46.46it/s][A

{'load_data': '0.000', 'extract_feat': '0.032', 'forward': '0.108', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 46.46it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 46.46it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 44.67it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 46.69it/s][A

{'load_data': '0.000', 'extract_feat': '0.032', 'forward': '0.107', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 46.69it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 46.69it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 44.89it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 46.70it/s][A

{'load_data': '0.000', 'extract_feat': '0.032', 'forward': '0.107', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 46.70it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 46.70it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 44.91it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.98it/s][A

{'load_data': '0.000', 'extract_feat': '0.034', 'forward': '0.109', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.98it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.98it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 44.25it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.93it/s][A

{'load_data': '0.000', 'extract_feat': '0.033', 'forward': '0.109', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.93it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.93it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 44.19it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.81it/s][A

{'load_data': '0.000', 'extract_feat': '0.034', 'forward': '0.109', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.81it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.81it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 44.07it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.81it/s][A

{'load_data': '0.000', 'extract_feat': '0.034', 'forward': '0.109', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.81it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.81it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 44.06it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.26it/s][A

{'load_data': '0.000', 'extract_feat': '0.035', 'forward': '0.110', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.26it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.26it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 43.56it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.12it/s][A

{'load_data': '0.000', 'extract_feat': '0.035', 'forward': '0.111', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.12it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 45.12it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 43.43it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 43.99it/s][A

{'load_data': '0.000', 'extract_feat': '0.037', 'forward': '0.114', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 43.99it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 43.99it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 42.35it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 44.32it/s][A

{'load_data': '0.000', 'extract_feat': '0.037', 'forward': '0.113', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 44.32it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 44.32it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 42.69it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 42.41it/s][A

{'load_data': '0.000', 'extract_feat': '0.035', 'forward': '0.118', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 42.41it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 42.41it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.91it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.85it/s][A

{'load_data': '0.000', 'extract_feat': '0.035', 'forward': '0.119', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.85it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.85it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.39it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.60it/s][A

{'load_data': '0.000', 'extract_feat': '0.036', 'forward': '0.120', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.60it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.60it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.15it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.67it/s][A

{'load_data': '0.000', 'extract_feat': '0.036', 'forward': '0.120', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.67it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.67it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.23it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.27it/s][A

{'load_data': '0.000', 'extract_feat': '0.036', 'forward': '0.121', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.27it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.27it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.85it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.31it/s][A

{'load_data': '0.000', 'extract_feat': '0.036', 'forward': '0.121', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.31it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 41.31it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.88it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.72it/s][A

{'load_data': '0.000', 'extract_feat': '0.036', 'forward': '0.123', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.72it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.72it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.34it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.45it/s][A

{'load_data': '0.000', 'extract_feat': '0.037', 'forward': '0.124', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.45it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.45it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.08it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.48it/s][A

{'load_data': '0.000', 'extract_feat': '0.036', 'forward': '0.124', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.48it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.48it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.11it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.55it/s][A

{'load_data': '0.000', 'extract_feat': '0.036', 'forward': '0.123', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.55it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 40.55it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.16it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.73it/s][A

{'load_data': '0.000', 'extract_feat': '0.037', 'forward': '0.126', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.73it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.73it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 38.33it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 38.22it/s][A

{'load_data': '0.000', 'extract_feat': '0.042', 'forward': '0.131', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 38.22it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 38.22it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 37.00it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.48it/s][A

{'load_data': '0.000', 'extract_feat': '0.038', 'forward': '0.127', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.48it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.48it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 38.17it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.57it/s][A

{'load_data': '0.000', 'extract_feat': '0.038', 'forward': '0.126', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.57it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.57it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 38.25it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 38.43it/s][A

{'load_data': '0.000', 'extract_feat': '0.042', 'forward': '0.130', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 38.43it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 38.43it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 37.18it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.47it/s][A

{'load_data': '0.000', 'extract_feat': '0.038', 'forward': '0.127', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.47it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.47it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 38.16it/s]


  0%|[34m          [0m| 0/5 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.46it/s][A

{'load_data': '0.000', 'extract_feat': '0.037', 'forward': '0.127', 'batch_size': '5', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.46it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 39.46it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 5/5 [00:00<00:00, 38.13it/s]


  0%|[34m          [0m| 0/4 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 4/4 [00:00<00:00, 38.09it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.105', 'batch_size': '4', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 4/4 [00:00<00:00, 38.09it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 38.09it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 36.59it/s]


  0%|[34m          [0m| 0/4 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 4/4 [00:00<00:00, 37.12it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.108', 'batch_size': '4', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 4/4 [00:00<00:00, 37.12it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 37.12it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 35.69it/s]


  0%|[34m          [0m| 0/4 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 4/4 [00:00<00:00, 37.21it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.107', 'batch_size': '4', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 4/4 [00:00<00:00, 37.21it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 37.21it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 35.76it/s]


  0%|[34m          [0m| 0/4 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 4/4 [00:00<00:00, 36.28it/s][A

{'load_data': '0.000', 'extract_feat': '0.032', 'forward': '0.110', 'batch_size': '4', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 4/4 [00:00<00:00, 36.28it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 36.28it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 34.91it/s]


  0%|[34m          [0m| 0/4 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 4/4 [00:00<00:00, 33.15it/s][A

{'load_data': '0.000', 'extract_feat': '0.034', 'forward': '0.121', 'batch_size': '4', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 4/4 [00:00<00:00, 33.15it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 33.15it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 32.01it/s]


  0%|[34m          [0m| 0/4 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 4/4 [00:00<00:00, 32.12it/s][A

{'load_data': '0.000', 'extract_feat': '0.035', 'forward': '0.125', 'batch_size': '4', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 4/4 [00:00<00:00, 32.12it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 32.12it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 31.04it/s]


  0%|[34m          [0m| 0/4 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 4/4 [00:00<00:00, 28.58it/s][A

{'load_data': '0.000', 'extract_feat': '0.036', 'forward': '0.140', 'batch_size': '4', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 4/4 [00:00<00:00, 28.58it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 28.58it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 27.58it/s]


  0%|[34m          [0m| 0/4 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 4/4 [00:00<00:00, 29.06it/s][A

{'load_data': '0.000', 'extract_feat': '0.042', 'forward': '0.138', 'batch_size': '4', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 4/4 [00:00<00:00, 29.06it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 29.06it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 28.15it/s]


  0%|[34m          [0m| 0/4 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 4/4 [00:00<00:00, 29.33it/s][A

{'load_data': '0.000', 'extract_feat': '0.038', 'forward': '0.136', 'batch_size': '4', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 4/4 [00:00<00:00, 29.33it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 29.33it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 28.20it/s]


  0%|[34m          [0m| 0/4 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 4/4 [00:00<00:00, 28.44it/s][A

{'load_data': '0.000', 'extract_feat': '0.041', 'forward': '0.141', 'batch_size': '4', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 4/4 [00:00<00:00, 28.44it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 28.44it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 4/4 [00:00<00:00, 27.19it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.64it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.122', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.64it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.64it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 23.61it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.47it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.47it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.47it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 23.45it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.30it/s][A

{'load_data': '0.000', 'extract_feat': '0.032', 'forward': '0.123', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.30it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.30it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 23.28it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.36it/s][A

{'load_data': '0.000', 'extract_feat': '0.032', 'forward': '0.123', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.36it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.36it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 23.35it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.45it/s][A

{'load_data': '0.000', 'extract_feat': '0.032', 'forward': '0.123', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.45it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.45it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 23.43it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.31it/s][A

{'load_data': '0.000', 'extract_feat': '0.032', 'forward': '0.123', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.31it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 24.31it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 23.29it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 23.22it/s][A

{'load_data': '0.000', 'extract_feat': '0.034', 'forward': '0.129', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 23.22it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 23.22it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 22.15it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 22.15it/s][A

{'load_data': '0.000', 'extract_feat': '0.036', 'forward': '0.135', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 22.15it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 22.15it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 21.17it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 22.01it/s][A

{'load_data': '0.000', 'extract_feat': '0.036', 'forward': '0.136', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 22.01it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 22.01it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 21.03it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 21.86it/s][A

{'load_data': '0.000', 'extract_feat': '0.036', 'forward': '0.137', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 21.86it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 21.86it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 20.91it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 21.26it/s][A

{'load_data': '0.000', 'extract_feat': '0.037', 'forward': '0.141', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 21.26it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 21.26it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 20.34it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 21.07it/s][A

{'load_data': '0.000', 'extract_feat': '0.038', 'forward': '0.142', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 21.07it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 21.07it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 20.17it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 20.20it/s][A

{'load_data': '0.000', 'extract_feat': '0.038', 'forward': '0.148', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 20.20it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 20.20it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 19.37it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 19.09it/s][A

{'load_data': '0.000', 'extract_feat': '0.041', 'forward': '0.157', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 19.09it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 19.09it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 18.37it/s]


  0%|[34m          [0m| 0/3 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 3/3 [00:00<00:00, 18.18it/s][A

{'load_data': '0.000', 'extract_feat': '0.046', 'forward': '0.165', 'batch_size': '3', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 3/3 [00:00<00:00, 18.18it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 18.18it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 3/3 [00:00<00:00, 17.47it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.39it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.39it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.39it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.81it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.28it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.28it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.28it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.71it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.18it/s][A

{'load_data': '0.000', 'extract_feat': '0.032', 'forward': '0.124', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.18it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.18it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.62it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.23it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.23it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.23it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.67it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.23it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.23it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.23it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.66it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.25it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.25it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.25it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.69it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.22it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.22it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.22it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.66it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.22it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.22it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.22it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.66it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.25it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.25it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.25it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.68it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.28it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.28it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.28it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.72it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.33it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.33it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.33it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.73it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.15it/s][A

{'load_data': '0.000', 'extract_feat': '0.032', 'forward': '0.124', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.15it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.15it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.59it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.09it/s][A

{'load_data': '0.000', 'extract_feat': '0.032', 'forward': '0.124', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.09it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.09it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.54it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.27it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.27it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.27it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.70it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.37it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.37it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.37it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.80it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.22it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.22it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.22it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.66it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.35it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.35it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.35it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.78it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.31it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.31it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.31it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.74it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.46it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.121', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.46it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.46it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.88it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.32it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.32it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.32it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.75it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.49it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.121', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.49it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.49it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.91it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.39it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.39it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.39it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.82it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.44it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.44it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.44it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.87it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.82it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.33it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.33it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.33it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.76it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.37it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.37it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.37it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.80it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.43it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.43it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.43it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.85it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.82it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.43it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.43it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.43it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.85it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.46it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.121', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.46it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.46it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.88it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.46it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.121', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.46it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.46it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.88it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.48it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.121', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.48it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.48it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.90it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.29it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.29it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.29it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.72it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.46it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.121', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.46it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.46it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.88it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.38it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.38it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.38it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.80it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.50it/s][A

{'load_data': '0.000', 'extract_feat': '0.029', 'forward': '0.121', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.50it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.50it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.92it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.82it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.44it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.44it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.44it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.86it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.15it/s][A

{'load_data': '0.000', 'extract_feat': '0.032', 'forward': '0.124', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.15it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.15it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.60it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.27it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.27it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.27it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.70it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.17it/s][A

{'load_data': '0.000', 'extract_feat': '0.032', 'forward': '0.124', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.17it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.17it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.61it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.25it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.25it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.25it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.69it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.19it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.19it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.19it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.63it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.17it/s][A

{'load_data': '0.000', 'extract_feat': '0.032', 'forward': '0.124', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.17it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.17it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.60it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.82it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.42it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.42it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.42it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.84it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.39it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.39it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.39it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.81it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.47it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.121', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.47it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.47it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.88it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.36it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.36it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.36it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.78it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.31it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.31it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.31it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.75it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.45it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.45it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.45it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.87it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.40it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.83it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.39it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.122', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.39it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.39it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.81it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.15it/s][A

{'load_data': '0.000', 'extract_feat': '0.030', 'forward': '0.124', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.15it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.15it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.60it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.21it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.21it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.21it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.65it/s]


  0%|[34m          [0m| 0/2 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.22it/s][A

{'load_data': '0.000', 'extract_feat': '0.031', 'forward': '0.123', 'batch_size': '2', 'rtf': '0.002'}, : 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.22it/s][A

rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 16.22it/s]                                                                                          [A
rtf_avg: 0.002: 100%|[34m██████████[0m| 2/2 [00:00<00:00, 15.66it/s]
Traceback (most recent call last):
  File "/data/funasr/funasr_api_zm.py", line 277, in transcribe
    transcription_result = self.model.generate(
  File "/root/miniconda3/envs/funasr/lib/python3.9/site-packages/funasr/auto/auto_model.py", line 306, in generate
    return self.inference_with_vad(input, input_len=input_len, **cfg)
  File "/root/miniconda3/envs/funasr/lib/python3.9/site-packages/funasr/auto/auto_model.py", line 614, in inference_with_vad
    punc_res[0]["punc_array"],
UnboundLocalError: local variable 'punc_res' referenced before assignment
Traceback (most recent call last):
  File "/data/funasr/funasr_api_zm.py", line 688, in asr_turbo
    text, transcription_result = transcriber.transcribe(wavname)
  File "/data/funasr/funasr_api_zm.py", line 277, in transcribe
    transcription_result = self.model.generate(
  File "/root/miniconda3/envs/funasr/lib/python3.9/site-packages/funasr/auto/auto_model.py", line 306, in generate
    return self.inference_with_vad(input, input_len=input_len, **cfg)
  File "/root/miniconda3/envs/funasr/lib/python3.9/site-packages/funasr/auto/auto_model.py", line 614, in inference_with_vad
    punc_res[0]["punc_array"],
UnboundLocalError: local variable 'punc_res' referenced before assignment

  0%|[31m          [0m| 0/1 [00:39<?, ?it/s]
转录过程中发生错误: local variable 'punc_res' referenced before assignment
API处理出错: local variable 'punc_res' referenced before assignment
INFO:     **************:61345 - "POST /api/sound/asr_turbo HTTP/1.1" 200 OK
